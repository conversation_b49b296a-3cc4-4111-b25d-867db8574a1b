-- Raw Database Query for <PERSON> - Claims P636C848 and P636C841
-- This provides all the underlying data used to generate the X12 bulk file

-- =====================================================
-- CLAIM BASIC INFORMATION
-- =====================================================
SELECT 'CLAIM_BASIC_INFO' as data_type, 
       c.id as claim_id,
       c.patient_control_number,
       c.date_of_service,
       c.total_claim_balance,
       c.total_pt_responsibility_balance,
       c.billing_branch_id,
       c.prescription_id,
       c.responsible_patient_insurance_id,
       c.user_id,
       c.created_at,
       c.updated_at
FROM claim c 
WHERE c.patient_control_number IN ('P636C848', 'P636C841')

UNION ALL

-- =====================================================
-- PATIENT INFORMATION
-- =====================================================
SELECT 'PATIENT_INFO' as data_type,
       CAST(c.id as CHAR) as claim_id,
       CONCAT('Patient: ', p.first_name, ' ', p.last_name) as patient_control_number,
       CAST(p.date_of_birth as CHAR) as date_of_service,
       p.gender as total_claim_balance,
       CONCAT('Address: ', p.address_line_1, ', ', p.city, ', ', p.state, ' ', p.zip_code) as total_pt_responsibility_balance,
       CAST(p.id as CHAR) as billing_branch_id,
       '' as prescription_id,
       '' as responsible_patient_insurance_id,
       '' as user_id,
       '' as created_at,
       '' as updated_at
FROM claim c 
JOIN prescription rx ON rx.id = c.prescription_id
JOIN patient p ON p.id = rx.patient_id
WHERE c.patient_control_number IN ('P636C848', 'P636C841')

UNION ALL

-- =====================================================
-- INSURANCE INFORMATION
-- =====================================================
SELECT 'INSURANCE_INFO' as data_type,
       CAST(c.id as CHAR) as claim_id,
       CONCAT('Insurance: ', ic.name) as patient_control_number,
       CONCAT('Member ID: ', pi.member_id) as date_of_service,
       CONCAT('Group: ', pi.group_number) as total_claim_balance,
       pi.carrier_type as total_pt_responsibility_balance,
       CONCAT('Payer ID: ', ic.payer_id) as billing_branch_id,
       CAST(pi.id as CHAR) as prescription_id,
       CAST(ic.id as CHAR) as responsible_patient_insurance_id,
       '' as user_id,
       '' as created_at,
       '' as updated_at
FROM claim c 
JOIN patient_insurance pi ON pi.id = c.responsible_patient_insurance_id
JOIN insurance_company ic ON ic.id = pi.insurance_company_id
WHERE c.patient_control_number IN ('P636C848', 'P636C841')

UNION ALL

-- =====================================================
-- SERVICE LINES (INSURANCE VERIFICATION L-CODES)
-- =====================================================
SELECT 'SERVICE_LINES' as data_type,
       CAST(c.id as CHAR) as claim_id,
       CONCAT('Line ', ivlc.line_number, ': ', lc.name) as patient_control_number,
       CAST(ivlc.date_of_service as CHAR) as date_of_service,
       CAST(ivlc.total_charge as CHAR) as total_claim_balance,
       CAST(ivlc.total_allowable as CHAR) as total_pt_responsibility_balance,
       CAST(ivlc.units as CHAR) as billing_branch_id,
       lc.hcpc_code as prescription_id,
       CONCAT('Modifiers: ', COALESCE(ivlc.modifier_1, ''), ' ', COALESCE(ivlc.modifier_2, ''), ' ', COALESCE(ivlc.modifier_3, ''), ' ', COALESCE(ivlc.modifier_4, '')) as responsible_patient_insurance_id,
       CAST(ivlc.id as CHAR) as user_id,
       '' as created_at,
       '' as updated_at
FROM claim c 
JOIN insurance_verification iv ON iv.prescription_id = c.prescription_id AND iv.patient_insurance_id = c.responsible_patient_insurance_id
JOIN insurance_verification_l_code ivlc ON ivlc.insurance_verification_id = iv.id
JOIN l_code lc ON lc.id = ivlc.l_code_id
WHERE c.patient_control_number IN ('P636C848', 'P636C841')
ORDER BY CAST(SUBSTRING_INDEX(patient_control_number, 'P636C', -1) as UNSIGNED), 
         CASE WHEN data_type = 'SERVICE_LINES' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(patient_control_number, 'Line ', -1), ':', 1) as UNSIGNED) ELSE 0 END;
