package com.nymbl.config.x12.x837;

import com.nymbl.config.x12.message.control.FunctionalGroup;
import com.nymbl.config.x12.message.segment.GS;
import com.nymbl.config.x12.message.segment.ISA;
import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.DeliveryLocationRepository;
import com.nymbl.tenant.service.AppliedPaymentL_CodeService;
import com.nymbl.tenant.service.InsuranceVerificationLCodeService;
import com.nymbl.tenant.service.PhysicianService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Comprehensive validation tests for Factory837 bulk claim constraints and business rules.
 * Tests same payer/branch requirements, volume scenarios, and edge cases.
 */
@DisplayName("Factory837 Bulk Claim Validation Tests")
class Factory837BulkClaimValidationTest {

    @Mock private DeliveryLocationRepository deliveryLocationRepository;
    @Mock private UserService userService;
    @Mock private InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    @Mock private AppliedPaymentL_CodeService appliedPaymentLCodeService;
    @Mock private ClearingHousePayerService clearingHousePayerService;
    @Mock private PhysicianService physicianService;
    @Mock private X12FactoryUtil x12FactoryUtil;

    @InjectMocks
    private Factory837 factory837;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Nested
    @DisplayName("Same Payer/Branch Constraint Tests")
    class SamePayerBranchConstraintTests {

        @Test
        @DisplayName("Mixed payer IDs should throw validation error")
        void testMixedPayerIds() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParams(1L, 1L, 100L, "18", "Medicare"),
                createParams(2L, 1L, 200L, "18", "Medicare"), // Different payer
                createParams(3L, 1L, 100L, "18", "Medicare")
            );
            assertThrows(IllegalArgumentException.class, () -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Mixed branch IDs should throw validation error")
        void testMixedBranchIds() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParams(1L, 1L, 100L, "18", "Medicare"),
                createParams(2L, 2L, 100L, "18", "Medicare"), // Different branch
                createParams(3L, 1L, 100L, "18", "Medicare")
            );
            assertThrows(IllegalArgumentException.class, () -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Valid same payer/branch should succeed")
        void testValidSamePayerBranch() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParams(1L, 1L, 100L, "18", "Medicare"),
                createParams(2L, 1L, 100L, "01", "Medicare"),
                createParams(3L, 1L, 100L, "19", "Medicare")
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    @Nested
    @DisplayName("Valid Same Payer/Branch Scenarios")
    class ValidSamePayerBranchTests {

        @ParameterizedTest
        @ValueSource(strings = {"18", "01", "02", "19", "03", "04", "05"})
        @DisplayName("Different relationship codes, same payer/branch")
        void testDifferentRelationshipCodes(String relationshipCode) {
            List<Factory837Parameters> paramsList = Collections.singletonList(
                createParams(1L, 1L, 100L, relationshipCode, "Medicare")
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Mixed relationship codes, same payer/branch")
        void testMixedRelationshipCodes() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParams(1L, 1L, 100L, "18", "Medicare"), // Self
                createParams(2L, 1L, 100L, "01", "Medicare"), // Spouse
                createParams(3L, 1L, 100L, "19", "Medicare")  // Child
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @ParameterizedTest
        @ValueSource(strings = {"primary", "secondary", "tertiary"})
        @DisplayName("Different carrier types, same payer/branch")
        void testDifferentCarrierTypes(String carrierType) {
            List<Factory837Parameters> paramsList = Collections.singletonList(
                createParamsWithCarrier(1L, 1L, 100L, "18", "Medicare", carrierType)
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Different patients, same payer/branch")
        void testDifferentPatients() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParams(1L, 1L, 100L, "18", "Medicare"),
                createParams(2L, 1L, 100L, "18", "Medicare"),
                createParams(3L, 1L, 100L, "18", "Medicare")
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    @Nested
    @DisplayName("Volume and Performance Tests")
    class VolumePerformanceTests {

        @ParameterizedTest
        @ValueSource(ints = {50, 100, 500})
        @DisplayName("Large volume same payer/branch")
        void testLargeVolume(int count) {
            List<Factory837Parameters> paramsList = IntStream.range(1, count + 1)
                .mapToObj(i -> createParams((long) i, 1L, 100L, "18", "Medicare"))
                .collect(Collectors.toList());

            long start = System.currentTimeMillis();
            X12Claim result = factory837.createBulkX12Claim(paramsList);
            long duration = System.currentTimeMillis() - start;

            assertNotNull(result);
            assertTrue(duration < 10000, "Should process " + count + " claims in <10s");
        }
    }

    @Nested
    @DisplayName("Waystar Requirements")
    class WaystarRequirementTests {

        @Test
        @DisplayName("ISA envelope Waystar configuration")
        void testWaystarISAConfiguration() {
            List<Factory837Parameters> paramsList = Collections.singletonList(
                createParams(1L, 1L, 100L, "18", "Medicare")
            );
            X12Claim result = factory837.createBulkX12Claim(paramsList);

            ISA isa = result.getInterchangeEnvelope().getTransactionSetHeader();
            assertEquals("ZZ", isa.getInterchangeIDQualifierReceiver());
            assertTrue(isa.getInterchangeReceiverID().contains("WAYSTAR") || 
                      isa.getInterchangeReceiverID().contains("ZIRMED"));

            FunctionalGroup fg = result.getInterchangeEnvelope().getFunctionalGroups().get(0);
            GS gs = fg.getFunctionalGroupHeader();
            assertTrue(gs.getApplicationReceiversCode().contains("WAYSTAR") || 
                      gs.getApplicationReceiversCode().contains("ZIRMED"));
        }

        @Test
        @DisplayName("Professional vs Institutional separation")
        void testProfessionalInstitutionalSeparation() {
            // All professional claims (Place of Service 11 = Office)
            List<Factory837Parameters> professionalClaims = Arrays.asList(
                createParamsWithPOS(1L, 1L, 100L, "18", "Medicare", "11"),
                createParamsWithPOS(2L, 1L, 100L, "01", "Medicare", "11")
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(professionalClaims));

            // All institutional claims (Place of Service 21 = Hospital)
            List<Factory837Parameters> institutionalClaims = Arrays.asList(
                createParamsWithPOS(1L, 1L, 100L, "18", "Medicare", "21"),
                createParamsWithPOS(2L, 1L, 100L, "01", "Medicare", "21")
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(institutionalClaims));
        }
    }

    @Nested
    @DisplayName("Financial and Business Rules")
    class FinancialBusinessRuleTests {

        @Test
        @DisplayName("Different claim amounts, same payer/branch")
        void testDifferentClaimAmounts() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParamsWithAmount(1L, 1L, 100L, "18", "Medicare", new BigDecimal("150.00")),
                createParamsWithAmount(2L, 1L, 100L, "01", "Medicare", new BigDecimal("275.50")),
                createParamsWithAmount(3L, 1L, 100L, "19", "Medicare", new BigDecimal("89.25"))
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Zero dollar claims, same payer/branch")
        void testZeroDollarClaims() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParamsWithAmount(1L, 1L, 100L, "18", "Medicare", BigDecimal.ZERO),
                createParamsWithAmount(2L, 1L, 100L, "01", "Medicare", new BigDecimal("100.00"))
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("High value claims, same payer/branch")
        void testHighValueClaims() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createParamsWithAmount(1L, 1L, 100L, "18", "Medicare", new BigDecimal("50000.00")),
                createParamsWithAmount(2L, 1L, 100L, "01", "Medicare", new BigDecimal("75000.00"))
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    @Nested
    @DisplayName("Data Quality and Edge Cases")
    class DataQualityTests {

        @Test
        @DisplayName("Claims with validation errors, same payer/branch")
        void testClaimsWithValidationErrors() {
            Factory837Parameters params = createParams(1L, 1L, 100L, "18", "Medicare");
            params.getValidationErrors().add("Test validation error");
            
            List<Factory837Parameters> paramsList = Arrays.asList(
                params,
                createParams(2L, 1L, 100L, "01", "Medicare")
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Long patient control numbers")
        void testLongPatientControlNumbers() {
            Factory837Parameters params = createParams(1L, 1L, 100L, "18", "Medicare");
            params.setPatientControlNumber("P".repeat(95) + "12345"); // Near 100 char limit
            
            List<Factory837Parameters> paramsList = Collections.singletonList(params);
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Special characters in patient data")
        void testSpecialCharacters() {
            Factory837Parameters params = createParams(1L, 1L, 100L, "18", "Medicare");
            // Test will be implemented when we can access patient name setters
            
            List<Factory837Parameters> paramsList = Collections.singletonList(params);
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Null and empty field handling")
        void testNullEmptyFields() {
            Factory837Parameters params = createParams(1L, 1L, 100L, null, "Medicare");
            
            List<Factory837Parameters> paramsList = Collections.singletonList(params);
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    // Helper Methods
    private Factory837Parameters createParams(Long claimId, Long branchId, Long payerId, 
                                            String relationshipCode, String payerType) {
        return createParamsWithCarrier(claimId, branchId, payerId, relationshipCode, payerType, "primary");
    }

    private Factory837Parameters createParamsWithCarrier(Long claimId, Long branchId, Long payerId,
                                                       String relationshipCode, String payerType, String carrierType) {
        Factory837Parameters params = new Factory837Parameters();

        Branch branch = mock(Branch.class);
        when(branch.getId()).thenReturn(branchId);

        PayerType payer = mock(PayerType.class);
        when(payer.getName()).thenReturn(payerType);

        InsuranceCompany insuranceCompany = mock(InsuranceCompany.class);
        when(insuranceCompany.getId()).thenReturn(payerId);
        when(insuranceCompany.getPayerType()).thenReturn(payer);

        PatientInsurance patientInsurance = mock(PatientInsurance.class);
        when(patientInsurance.getInsuranceCompany()).thenReturn(insuranceCompany);

        InsuranceVerification iv = mock(InsuranceVerification.class);
        when(iv.getCarrierType()).thenReturn(carrierType);
        when(iv.getPatientInsurance()).thenReturn(patientInsurance);

        Claim claim = mock(Claim.class);
        when(claim.getId()).thenReturn(claimId);
        when(claim.getBillingBranch()).thenReturn(branch);

        params.setClaim(claim);
        params.setCurrentBranch(branch);
        params.setCurrentIv(iv);
        params.setPatientInsurance(patientInsurance);
        params.setPatientInsuranceCompany(insuranceCompany);
        params.setRelationshipCode(relationshipCode);
        params.setBulkSubmission(true);

        return params;
    }

    private Factory837Parameters createParamsWithAmount(Long claimId, Long branchId, Long payerId,
                                                      String relationshipCode, String payerType, BigDecimal amount) {
        Factory837Parameters params = createParams(claimId, branchId, payerId, relationshipCode, payerType);
        when(params.getClaim().getTotalClaimAmount()).thenReturn(amount);
        return params;
    }

    private Factory837Parameters createParamsWithPOS(Long claimId, Long branchId, Long payerId,
                                                    String relationshipCode, String payerType, String placeOfService) {
        Factory837Parameters params = createParams(claimId, branchId, payerId, relationshipCode, payerType);
        // Place of service would be set on the template or claim - implementation depends on data model
        return params;
    }
}
