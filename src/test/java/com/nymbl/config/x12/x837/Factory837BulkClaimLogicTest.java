package com.nymbl.config.x12.x837;

import com.nymbl.config.x12.message.control.FunctionalGroup;
import com.nymbl.config.x12.message.segment.BHT;
import com.nymbl.config.x12.message.segment.GS;
import com.nymbl.config.x12.message.segment.ISA;
import com.nymbl.config.x12.message.segment.ST;
import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.config.x12.x835.Factory835;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.DeliveryLocationRepository;
import com.nymbl.tenant.service.AppliedPaymentL_CodeService;
import com.nymbl.tenant.service.InsuranceVerificationLCodeService;
import com.nymbl.tenant.service.PhysicianService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Focused test for Factory837 bulk claim logic.
 * Tests the core conditional logic for self-insured vs dependent claims
 * without getting bogged down in complex object creation.
 */
@DisplayName("Factory837 Bulk Claim Logic Tests")
class Factory837BulkClaimLogicTest {

    @Mock private DeliveryLocationRepository deliveryLocationRepository;
    @Mock private UserService userService;
    @Mock private InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    @Mock private AppliedPaymentL_CodeService appliedPaymentLCodeService;
    @Mock private ClearingHousePayerService clearingHousePayerService;
    @Mock private PhysicianService physicianService;
    @Mock private X12FactoryUtil x12FactoryUtil;
    @Mock private Factory835 factory835;

    @InjectMocks
    private Factory837 factory837;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Test
    @DisplayName("All self-insured claims (relationship code 18) should use different logic")
    void testAllSelfInsuredClaims() {
        // Setup: Create self-insured claims (relationship code "18")
        List<Factory837Parameters> paramsList = Arrays.asList(
            createMockedParams(1L, "18", "Medicare"),
            createMockedParams(2L, "18", "Medicaid"),
            createMockedParams(3L, "18", "Group")
        );

        // Execute: This should not throw exceptions and should handle self-insured logic
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify: Should create a valid X12Claim
        assertNotNull(result);
    }

    @Test
    @DisplayName("All dependent claims (non-18 relationship codes) should use different logic")
    void testAllDependentClaims() {
        // Setup: Create dependent claims (relationship codes other than "18")
        List<Factory837Parameters> paramsList = Arrays.asList(
            createMockedParams(1L, "01", "Group"), // Spouse
            createMockedParams(2L, "19", "Group"), // Child
            createMockedParams(3L, "02", "Group")  // Dependent
        );

        // Execute: This should not throw exceptions and should handle dependent logic
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify: Should create a valid X12Claim
        assertNotNull(result);
    }

    @Test
    @DisplayName("Mixed self-insured and dependent claims should handle correctly")
    void testMixedClaims() {
        // Setup: Mix of self-insured and dependent claims
        List<Factory837Parameters> paramsList = Arrays.asList(
            createMockedParams(1L, "18", "Medicare"),  // Self-insured
            createMockedParams(2L, "01", "Medicaid"), // Spouse
            createMockedParams(3L, "18", "Group")     // Self-insured
        );

        // Execute: This should not throw exceptions and should handle mixed logic
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify: Should create a valid X12Claim
        assertNotNull(result);
    }

    @Test
    @DisplayName("Empty parameters list should throw IllegalArgumentException")
    void testEmptyParametersList() {
        // Execute & Verify
        assertThrows(IllegalArgumentException.class, 
            () -> factory837.createBulkX12Claim(Collections.emptyList()));
    }

    @Test
    @DisplayName("Single self-insured claim should work")
    void testSingleSelfInsuredClaim() {
        // Setup
        List<Factory837Parameters> paramsList = Collections.singletonList(
            createMockedParams(1L, "18", "Medicare")
        );

        // Execute
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify
        assertNotNull(result);
    }

    @Test
    @DisplayName("Single dependent claim should work")
    void testSingleDependentClaim() {
        // Setup
        List<Factory837Parameters> paramsList = Collections.singletonList(
            createMockedParams(1L, "01", "Group")
        );

        // Execute
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify
        assertNotNull(result);
    }

    @Test
    @DisplayName("Validate basic X12 structure is created")
    void testBasicX12Structure() {
        // Setup: Create simple claims
        List<Factory837Parameters> paramsList = Arrays.asList(
            createMockedParams(1L, "18", "Medicare"),
            createMockedParams(2L, "01", "Group")
        );

        // Execute
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify basic X12 structure
        assertNotNull(result);
        assertNotNull(result.getInterchangeEnvelope());
        assertNotNull(result.getInterchangeEnvelope().getFunctionalGroups());
        assertFalse(result.getInterchangeEnvelope().getFunctionalGroups().isEmpty());

        FunctionalGroup fg = result.getInterchangeEnvelope().getFunctionalGroups().get(0);
        assertNotNull(fg.getTransactions());
        assertFalse(fg.getTransactions().isEmpty());

        // Should have at least one transaction
        assertTrue(fg.getTransactions().size() > 0);
    }

    @Test
    @DisplayName("Validate segment count accuracy for bulk claims")
    void testSegmentCountAccuracy() {
        // Setup: Create multiple claims to test segment counting
        List<Factory837Parameters> paramsList = Arrays.asList(
            createMockedParams(1L, "18", "Medicare"),
            createMockedParams(2L, "18", "Medicaid"),
            createMockedParams(3L, "01", "Group")
        );

        // Execute
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify segment count is accurate
        FunctionalGroup fg = result.getInterchangeEnvelope().getFunctionalGroups().get(0);
        X12ClaimTransaction transaction = (X12ClaimTransaction) fg.getTransactions().get(0);

        // Get the SE segment count
        String segmentCountStr = transaction.getTransactionSetTrailer().getTransactionSegmentCount();
        int reportedCount = Integer.parseInt(segmentCountStr);

        // Verify the count is reasonable (should be > 10 for multiple claims)
        assertTrue(reportedCount > 10, "Segment count should be greater than 10 for multiple claims");

        // Verify the count is not excessive (should be < 1000 for a few claims)
        assertTrue(reportedCount < 1000, "Segment count should be reasonable for a few claims");
    }

    @Test
    @DisplayName("Validate segment count consistency between single and bulk claims")
    void testSegmentCountConsistency() {
        // Setup: Create same claim as single vs bulk
        Factory837Parameters singleParam = createMockedParams(1L, "18", "Medicare");
        List<Factory837Parameters> bulkParams = Collections.singletonList(singleParam);

        // Execute bulk claim
        X12Claim bulkResult = factory837.createBulkX12Claim(bulkParams);

        // Verify bulk claim has reasonable segment count
        FunctionalGroup fg = bulkResult.getInterchangeEnvelope().getFunctionalGroups().get(0);
        X12ClaimTransaction transaction = (X12ClaimTransaction) fg.getTransactions().get(0);
        String bulkSegmentCountStr = transaction.getTransactionSetTrailer().getTransactionSegmentCount();
        int bulkCount = Integer.parseInt(bulkSegmentCountStr);

        // Verify bulk count is reasonable for single claim
        assertTrue(bulkCount > 5, "Bulk segment count should be greater than 5 for single claim");
        assertTrue(bulkCount < 100, "Bulk segment count should be less than 100 for single claim");
    }

    @Test
    @DisplayName("Validate required X12 segments are present in bulk claims")
    void testRequiredSegmentsPresent() {
        // Setup: Create multiple claims
        List<Factory837Parameters> paramsList = Arrays.asList(
            createMockedParams(1L, "18", "Medicare"),
            createMockedParams(2L, "01", "Group")
        );

        // Execute
        X12Claim result = factory837.createBulkX12Claim(paramsList);

        // Verify basic X12 structure exists
        assertNotNull(result.getInterchangeEnvelope(), "InterchangeEnvelope missing");
        assertNotNull(result.getInterchangeEnvelope().getTransactionSetHeader(), "ISA segment missing");
        assertNotNull(result.getInterchangeEnvelope().getTransactionSetTrailer(), "IEA segment missing");

        FunctionalGroup fg = result.getInterchangeEnvelope().getFunctionalGroups().get(0);
        assertNotNull(fg.getFunctionalGroupHeader(), "GS segment missing");
        assertNotNull(fg.getFunctionalGroupTrailer(), "GE segment missing");

        X12ClaimTransaction transaction = (X12ClaimTransaction) fg.getTransactions().get(0);
        assertNotNull(transaction.getTransactionSetHeader(), "ST segment missing");
        assertNotNull(transaction.getTransactionSetTrailer(), "SE segment missing");
        assertNotNull(transaction.getBeginningOfHierarchicalTransaction(), "BHT segment missing");

        // Verify the transaction has the expected structure
        assertTrue(fg.getTransactions().size() > 0, "Should have at least one transaction");
    }

    /**
     * Creates a simple mocked Factory837Parameters with minimal required fields
     * to test the bulk claim logic without complex object creation.
     */
    private Factory837Parameters createMockedParams(Long id, String relationshipCode, String payerType) {
        Factory837Parameters params = new Factory837Parameters();

        // Mock basic objects with minimal required fields
        Branch branch = mock(Branch.class);
        Patient patient = mock(Patient.class);
        Prescription prescription = mock(Prescription.class);
        Claim claim = mock(Claim.class);
        Form1500Template template = mock(Form1500Template.class);
        PayerType payer = mock(PayerType.class);
        InsuranceCompany insuranceCompany = mock(InsuranceCompany.class);
        InsuranceCompanyBranch insuranceCompanyBranch = mock(InsuranceCompanyBranch.class);
        PatientInsurance patientInsurance = mock(PatientInsurance.class);
        InsuranceVerification iv = mock(InsuranceVerification.class);

        // Set up basic relationships and mock required methods
        when(claim.getBillingBranch()).thenReturn(branch);
        when(claim.getTotalClaimAmount()).thenReturn(new BigDecimal("100.00"));
        when(prescription.getPatient()).thenReturn(patient);
        when(patient.getLastName()).thenReturn("Patient" + id);
        when(patient.getFirstName()).thenReturn("John");
        when(patient.getMiddleName()).thenReturn("M");
        when(patientInsurance.getInsuranceCompany()).thenReturn(insuranceCompany);
        when(patientInsurance.getGroupNumber()).thenReturn("GROUP" + id);
        when(patientInsurance.getInsuranceNumber()).thenReturn("INS" + id);
        when(insuranceCompany.getPayerType()).thenReturn(payer);
        when(insuranceCompany.getPhysicianToUse()).thenReturn("referring_physician");
        when(insuranceCompanyBranch.getStreetAddress()).thenReturn("123 Insurance St");
        when(payer.getName()).thenReturn(payerType);
        when(iv.getCarrierType()).thenReturn("primary");
        when(iv.getPatientInsurance()).thenReturn(patientInsurance);
        when(template.getBox24BPlaceOfService()).thenReturn(com.nymbl.config.enums.form1500.PlaceOfService.Office);

        // Set the required fields in params
        params.setClaim(claim);
        params.setPrescription(prescription);
        params.setForm1500Template(template);
        params.setCurrentBranch(branch);
        params.setCurrentIv(iv);
        params.setPatientInsurance(patientInsurance);
        params.setPatientInsuranceCompany(insuranceCompany);
        params.setPatientInsuranceCompanyBranch(insuranceCompanyBranch);
        params.setPlcs(new java.util.ArrayList<>());
        params.setIvlcs(new java.util.ArrayList<>());
        params.setRelationshipCode(relationshipCode);
        params.setAccountNumber("ACC" + id);

        return params;
    }
}
