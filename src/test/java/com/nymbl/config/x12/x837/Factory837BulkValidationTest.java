package com.nymbl.config.x12.x837;

import com.nymbl.config.x12.message.control.FunctionalGroup;
import com.nymbl.config.x12.message.segment.GS;
import com.nymbl.config.x12.message.segment.ISA;
import com.nymbl.config.x12.util.X12FactoryUtil;
import com.nymbl.master.service.ClearingHousePayerService;
import com.nymbl.master.service.UserService;
import com.nymbl.tenant.model.*;
import com.nymbl.tenant.repository.DeliveryLocationRepository;
import com.nymbl.tenant.service.AppliedPaymentL_CodeService;
import com.nymbl.tenant.service.InsuranceVerificationLCodeService;
import com.nymbl.tenant.service.PhysicianService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

/**
 * Comprehensive validation tests for Factory837 bulk claim constraints and business rules.
 * Tests same payer/branch requirements, volume scenarios, and edge cases.
 */
@DisplayName("Factory837 Bulk Validation Tests")
class Factory837BulkValidationTest {

    @Mock private DeliveryLocationRepository deliveryLocationRepository;
    @Mock private UserService userService;
    @Mock private InsuranceVerificationLCodeService insuranceVerificationLCodeService;
    @Mock private AppliedPaymentL_CodeService appliedPaymentLCodeService;
    @Mock private ClearingHousePayerService clearingHousePayerService;
    @Mock private PhysicianService physicianService;
    @Mock private X12FactoryUtil x12FactoryUtil;

    @InjectMocks
    private Factory837 factory837;

    @BeforeEach
    void setUp() {
        openMocks(this);
    }

    @Nested
    @DisplayName("Same Payer/Branch Constraint Tests")
    class SamePayerBranchConstraintTests {

        @Test
        @DisplayName("Mixed payer IDs should throw validation error")
        void testMixedPayerIds() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L),
                createMockedParams(2L, "18", "Medicare", 1L, 200L), // Different payer
                createMockedParams(3L, "18", "Medicare", 1L, 100L)
            );
            assertThrows(IllegalArgumentException.class, () -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Mixed branch IDs should throw validation error")
        void testMixedBranchIds() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L),
                createMockedParams(2L, "18", "Medicare", 2L, 100L), // Different branch
                createMockedParams(3L, "18", "Medicare", 1L, 100L)
            );
            assertThrows(IllegalArgumentException.class, () -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Valid same payer/branch should succeed")
        void testValidSamePayerBranch() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L),
                createMockedParams(2L, "01", "Medicare", 1L, 100L),
                createMockedParams(3L, "19", "Medicare", 1L, 100L)
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    @Nested
    @DisplayName("Valid Same Payer/Branch Scenarios")
    class ValidSamePayerBranchTests {

        @ParameterizedTest
        @ValueSource(strings = {"18", "01", "02", "19", "03", "04", "05"})
        @DisplayName("Different relationship codes, same payer/branch")
        void testDifferentRelationshipCodes(String relationshipCode) {
            List<Factory837Parameters> paramsList = Collections.singletonList(
                createMockedParams(1L, relationshipCode, "Medicare", 1L, 100L)
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Mixed relationship codes, same payer/branch")
        void testMixedRelationshipCodes() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L), // Self
                createMockedParams(2L, "01", "Medicare", 1L, 100L), // Spouse
                createMockedParams(3L, "19", "Medicare", 1L, 100L)  // Child
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Different patients, same payer/branch")
        void testDifferentPatients() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L),
                createMockedParams(2L, "18", "Medicare", 1L, 100L),
                createMockedParams(3L, "18", "Medicare", 1L, 100L)
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    @Nested
    @DisplayName("Volume and Performance Tests")
    class VolumePerformanceTests {

        @ParameterizedTest
        @ValueSource(ints = {10, 25, 50})
        @DisplayName("Large volume same payer/branch")
        void testLargeVolume(int count) {
            List<Factory837Parameters> paramsList = IntStream.range(1, count + 1)
                .mapToObj(i -> createMockedParams((long) i, "18", "Medicare", 1L, 100L))
                .collect(Collectors.toList());

            long start = System.currentTimeMillis();
            X12Claim result = factory837.createBulkX12Claim(paramsList);
            long duration = System.currentTimeMillis() - start;

            assertNotNull(result);
            assertTrue(duration < 5000, "Should process " + count + " claims in <5s");
        }
    }

    @Nested
    @DisplayName("Waystar Requirements")
    class WaystarRequirementTests {

        @Test
        @DisplayName("ISA envelope Waystar configuration")
        void testWaystarISAConfiguration() {
            List<Factory837Parameters> paramsList = Collections.singletonList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L)
            );
            X12Claim result = factory837.createBulkX12Claim(paramsList);

            ISA isa = result.getInterchangeEnvelope().getTransactionSetHeader();
            assertEquals("ZZ", isa.getInterchangeIDQualifierReceiver());
            assertTrue(isa.getInterchangeReceiverID().contains("WAYSTAR") || 
                      isa.getInterchangeReceiverID().contains("ZIRMED"));

            FunctionalGroup fg = result.getInterchangeEnvelope().getFunctionalGroups().get(0);
            GS gs = fg.getFunctionalGroupHeader();
            assertTrue(gs.getApplicationReceiversCode().contains("WAYSTAR") || 
                      gs.getApplicationReceiversCode().contains("ZIRMED"));
        }
    }

    @Nested
    @DisplayName("Financial and Business Rules")
    class FinancialBusinessRuleTests {

        @Test
        @DisplayName("Different claim amounts, same payer/branch")
        void testDifferentClaimAmounts() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParamsWithAmount(1L, "18", "Medicare", 1L, 100L, new BigDecimal("150.00")),
                createMockedParamsWithAmount(2L, "01", "Medicare", 1L, 100L, new BigDecimal("275.50")),
                createMockedParamsWithAmount(3L, "19", "Medicare", 1L, 100L, new BigDecimal("89.25"))
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Zero dollar claims, same payer/branch")
        void testZeroDollarClaims() {
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParamsWithAmount(1L, "18", "Medicare", 1L, 100L, BigDecimal.ZERO),
                createMockedParamsWithAmount(2L, "01", "Medicare", 1L, 100L, new BigDecimal("100.00"))
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    @Nested
    @DisplayName("Data Quality and Edge Cases")
    class DataQualityTests {

        @Test
        @DisplayName("Claims with validation errors, same payer/branch")
        void testClaimsWithValidationErrors() {
            Factory837Parameters params = createMockedParams(1L, "18", "Medicare", 1L, 100L);
            params.getValidationErrors().add("Test validation error");
            
            List<Factory837Parameters> paramsList = Arrays.asList(
                params,
                createMockedParams(2L, "01", "Medicare", 1L, 100L)
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }

        @Test
        @DisplayName("Null relationship code handling")
        void testNullRelationshipCode() {
            List<Factory837Parameters> paramsList = Collections.singletonList(
                createMockedParams(1L, null, "Medicare", 1L, 100L)
            );
            assertDoesNotThrow(() -> factory837.createBulkX12Claim(paramsList));
        }
    }

    // Helper Methods - Copy working pattern from other test files
    private Factory837Parameters createMockedParams(Long claimId, String relationshipCode, String payerType, Long branchId, Long payerId) {
        return createMockedParamsWithAmount(claimId, relationshipCode, payerType, branchId, payerId, new BigDecimal("100.00"));
    }

    private Factory837Parameters createMockedParamsWithAmount(Long claimId, String relationshipCode, String payerType, Long branchId, Long payerId, BigDecimal amount) {
        Factory837Parameters params = new Factory837Parameters();

        // Use mocks - copy exact pattern from working test files
        Branch branch = mock(Branch.class);
        when(branch.getId()).thenReturn(branchId);
        when(branch.getName()).thenReturn("Test Branch " + branchId);

        Patient patient = mock(Patient.class);
        when(patient.getId()).thenReturn(claimId);
        when(patient.getLastName()).thenReturn("TestLast" + claimId);
        when(patient.getFirstName()).thenReturn("TestFirst" + claimId);

        Prescription prescription = mock(Prescription.class);
        when(prescription.getId()).thenReturn(claimId);
        when(prescription.getPatient()).thenReturn(patient);

        Claim claim = mock(Claim.class);
        when(claim.getId()).thenReturn(claimId);
        when(claim.getBillingBranch()).thenReturn(branch);
        when(claim.getTotalClaimAmount()).thenReturn(amount);

        Form1500Template template = mock(Form1500Template.class);
        when(template.getUseCapitatedPayerSpecialEdits()).thenReturn(false);
        when(template.getBox33BBillingTaxonomy()).thenReturn("123456789X");

        // Mock PlaceOfService enum
        com.nymbl.config.enums.form1500.PlaceOfService placeOfService = mock(com.nymbl.config.enums.form1500.PlaceOfService.class);
        when(placeOfService.getValue()).thenReturn("11"); // Office
        when(template.getBox24BPlaceOfService()).thenReturn(placeOfService);

        PayerType payer = mock(PayerType.class);
        when(payer.getName()).thenReturn(payerType);

        InsuranceCompany insuranceCompany = mock(InsuranceCompany.class);
        when(insuranceCompany.getId()).thenReturn(payerId);
        when(insuranceCompany.getPayerType()).thenReturn(payer);
        when(insuranceCompany.getPhysicianToUse()).thenReturn("REFERRING_PHYSICIAN");
        when(insuranceCompany.getPhysicianQualifier()).thenReturn("DN");

        // Mock InsuranceCompanyBranch
        InsuranceCompanyBranch insuranceCompanyBranch = mock(InsuranceCompanyBranch.class);
        when(insuranceCompanyBranch.getStreetAddress()).thenReturn("123 Test St");
        when(insuranceCompanyBranch.getCity()).thenReturn("Test City");
        when(insuranceCompanyBranch.getState()).thenReturn("TX");
        when(insuranceCompanyBranch.getZipcode()).thenReturn("12345");

        PatientInsurance patientInsurance = mock(PatientInsurance.class);
        when(patientInsurance.getInsuranceCompany()).thenReturn(insuranceCompany);
        when(patientInsurance.getInsuranceNumber()).thenReturn("INS" + claimId);

        InsuranceVerification iv = mock(InsuranceVerification.class);
        when(iv.getCarrierType()).thenReturn("primary");
        when(iv.getPatientInsurance()).thenReturn(patientInsurance);

        // Mock PLCs list (empty list is fine for testing)
        java.util.List<Prescription_L_Code> plcsList = new java.util.ArrayList<>();

        // Set all fields
        params.setClaim(claim);
        params.setPrescription(prescription);
        params.setCurrentBranch(branch);
        params.setForm1500Template(template);
        params.setCurrentIv(iv);
        params.setPrimaryIv(iv);
        params.setPatientInsurance(patientInsurance);
        params.setPatientInsuranceCompany(insuranceCompany);
        params.setPatientInsuranceCompanyBranch(insuranceCompanyBranch);
        params.setPlcs(plcsList);
        params.setRelationshipCode(relationshipCode);
        params.setAccountNumber("ACC" + claimId);
        params.setDate("250101");
        params.setTime("1200");
        params.setDate8("********");
        params.setControlNumber("CTRL" + claimId);
        params.setPatientControlNumber("PCT" + claimId);
        params.setBulkSubmission(true);

        return params;
    }

    /**
     * STRESS TESTS: These will reveal actual issues in your bulk implementation
     */
    @Nested
    @DisplayName("Factory837 Implementation Stress Tests")
    class Factory837StressTests {

        @Test
        @DisplayName("Test actual createBulkX12Claim - find real issues")
        void testActualBulkX12ClaimGeneration() {
            // Test the ACTUAL method that exists
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L),
                createMockedParams(2L, "01", "Medicare", 1L, 100L),
                createMockedParams(3L, "19", "Medicare", 1L, 100L)
            );

            // Use the @InjectMocks factory that's already configured
            assertDoesNotThrow(() -> {
                X12Claim x12Claim = factory837.createBulkX12Claim(paramsList);
                assertNotNull(x12Claim, "X12Claim should not be null");

                // Check for validation errors in any of the parameters
                for (int i = 0; i < paramsList.size(); i++) {
                    Factory837Parameters params = paramsList.get(i);
                    if (!params.getValidationErrors().isEmpty()) {
                        System.out.println("FOUND VALIDATION ERRORS in claim " + i + ": " + params.getValidationErrors());
                    }
                    System.out.println("Claim " + i + " segment count: " + params.getSegmentCount());
                }

                // Test the actual X12Claim structure
                assertNotNull(x12Claim.getInterchangeEnvelope(), "ISA envelope should not be null");
                assertFalse(x12Claim.getInterchangeEnvelope().getFunctionalGroups().isEmpty(), "Should have functional groups");

                FunctionalGroup fg = x12Claim.getInterchangeEnvelope().getFunctionalGroups().get(0);
                assertFalse(fg.getTransactions().isEmpty(), "Should have transactions");

                // Cast to X12ClaimTransaction to check structure
                X12ClaimTransaction transaction = (X12ClaimTransaction) fg.getTransactions().get(0);
                System.out.println("Loop2000A count: " + transaction.getLoop2000AList().size());
                System.out.println("Loop2000B count: " + transaction.getLoop2000BList().size());
                System.out.println("Loop2000C count: " + transaction.getLoop2000CList().size());
            });
        }

        @Test
        @DisplayName("Test Bradley's specific issues - segment count and loop structure")
        void testBradleySpecificIssues() {
            // Test the actual createBulkX12Claim method to find Bradley's issues
            List<Factory837Parameters> paramsList = Arrays.asList(
                createMockedParams(1L, "18", "Medicare", 1L, 100L),
                createMockedParams(2L, "01", "Medicare", 1L, 100L)
            );

            assertDoesNotThrow(() -> {
                X12Claim x12Claim = factory837.createBulkX12Claim(paramsList);
                assertNotNull(x12Claim, "X12Claim should not be null");

                // Check the transaction structure to find Bradley's issues
                FunctionalGroup fg = x12Claim.getInterchangeEnvelope().getFunctionalGroups().get(0);
                X12ClaimTransaction transaction = (X12ClaimTransaction) fg.getTransactions().get(0);

                // Check for Bradley's specific issues:
                // 1. Extra subscriber loop
                System.out.println("Loop2000A count: " + transaction.getLoop2000AList().size());
                System.out.println("Loop2000B count: " + transaction.getLoop2000BList().size());
                System.out.println("Loop2000C count: " + transaction.getLoop2000CList().size());

                // 2. Missing segments - check if transaction has required segments
                assertNotNull(transaction.getTransactionSetHeader(), "ST segment missing");
                assertNotNull(transaction.getBeginningOfHierarchicalTransaction(), "BHT segment missing");
                assertNotNull(transaction.getTransactionSetTrailer(), "SE segment missing");

                // 3. Incorrect segment count - check SE segment
                String segmentCount = transaction.getTransactionSetTrailer().getTransactionSegmentCount();
                System.out.println("SE segment count: " + segmentCount);

                // 4. Check for validation errors in parameters
                for (int i = 0; i < paramsList.size(); i++) {
                    Factory837Parameters params = paramsList.get(i);
                    if (!params.getValidationErrors().isEmpty()) {
                        System.out.println("VALIDATION ERRORS in claim " + i + ": " + params.getValidationErrors());
                    }
                }

                // This test will reveal the actual structure and any issues
                System.out.println("Bulk X12 claim generation completed successfully");
            });
        }
    }
}
